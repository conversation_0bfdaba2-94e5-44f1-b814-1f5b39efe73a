name: SonarCloud Analysis

on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - main

jobs:
  sonarcloud:
    name: SonarCloud Scan
    runs-on: ubuntu-latest

    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Setup Java (Required for SonarCloud)
        uses: actions/setup-java@v3
        with:
          distribution: 'temurin'
          java-version: '17'

      - name: Cache SonarCloud packages
        uses: actions/cache@v3
        with:
          path: ~/.sonar/cache
          key: sonar-${{ runner.os }}
          restore-keys: sonar-

      - name: Run SonarCloud Scan
        run: |
          npx sonar-scanner \
            -Dsonar.organization=overide-phoenix \
            -Dsonar.projectKey=OveRide-Phoenix_kk_v1 \
            -Dsonar.sources=. \
            -Dsonar.host.url=https://sonarcloud.io \
            -Dsonar.login=${{ secrets.SONAR_TOKEN }}
