# You can override the included template(s) by including variable overrides
# SAST customization: https://docs.gitlab.com/ee/user/application_security/sast/#customizing-the-sast-settings
# Secret Detection customization: https://docs.gitlab.com/ee/user/application_security/secret_detection/pipeline/#customization
# Dependency Scanning customization: https://docs.gitlab.com/ee/user/application_security/dependency_scanning/#customizing-the-dependency-scanning-settings
# Container Scanning customization: https://docs.gitlab.com/ee/user/application_security/container_scanning/#customizing-the-container-scanning-settings
# Note that environment variables can be set in several places
# See https://docs.gitlab.com/ee/ci/variables/#cicd-variable-precedence
stages:
- test
- sonarqube
code_quality:
  stage: test
  image: registry.gitlab.com/gitlab-org/ci-cd/codequality:latest
  script:
  - "/codequality"
  allow_failure: false
  artifacts:
    reports:
      codequality: gl-code-quality-report.json
sonarqube:
  stage: sonarqube
  image: sonarsource/sonar-scanner-cli
  before_script:
  - curl -Lo jq https://github.com/stedolan/jq/releases/latest/download/jq-linux64
  - chmod +x jq
  - export PATH=$PWD:$PATH
  script:
  - |
    if [ -z "$CI_MERGE_REQUEST_ID" ]; then
      echo "No merge request found. Skipping SonarQube analysis."
      exit 0
    fi
  - sonar-scanner -Dsonar.token="$SONAR_TOKEN"
  - |
    RESPONSE=$(curl -s "https://sonarcloud.io/api/issues/search?componentKeys=OveRide-Phoenix_kk_v1")
    echo "SonarQube Response: $RESPONSE"
    SONAR_ISSUES=$(echo "$RESPONSE" | jq '.total')
    echo "Total SonarQube Issues: $SONAR_ISSUES"

    if [ "$SONAR_ISSUES" -gt 0 ]; then
      # Fetch MR details
      MR_RESPONSE=$(curl -s --header "PRIVATE-TOKEN: $GITLAB_TOKEN" \
        "https://gitlab.com/api/v4/projects/$CI_PROJECT_ID/merge_requests/$CI_MERGE_REQUEST_ID")

      echo "Merge Request Response: $MR_RESPONSE"

      MR_AUTHOR=$(echo "$MR_RESPONSE" | jq -r '.author.username // empty')
      SOURCE_BRANCH=$(echo "$MR_RESPONSE" | jq -r '.source_branch // empty')

      if [ -z "$MR_AUTHOR" ] || [ -z "$SOURCE_BRANCH" ]; then
        echo "Error: Could not fetch MR details"
        exit 1
      fi

      echo "MR Author: $MR_AUTHOR"
      echo "Source Branch: $SOURCE_BRANCH"

      # Loop through all issues and create separate GitLab issues
      echo "$RESPONSE" | jq -c '.issues[]' | while read issue; do
        ISSUE_TITLE=$(echo "$issue" | jq -r '.message')
        ISSUE_COMPONENT=$(echo "$issue" | jq -r '.component')
        ISSUE_LINE=$(echo "$issue" | jq -r '.line // "unknown"')

        curl --request POST --header "PRIVATE-TOKEN: $GITLAB_TOKEN" \
          --header "Content-Type: application/json" \
          --data "{
            \"title\": \"SonarQube Issue: $ISSUE_TITLE\",
            \"description\": \"Issue in $ISSUE_COMPONENT at line $ISSUE_LINE.\n\nFix required by @$MR_AUTHOR in branch '$SOURCE_BRANCH'\",
            \"assignee_ids\": [\"$MR_AUTHOR\"]
          }" \
          "https://gitlab.com/api/v4/projects/$CI_PROJECT_ID/issues"

      done
    fi
  allow_failure: true
sast:
  stage: test
include:
- template: Security/SAST.gitlab-ci.yml
- template: Security/Dependency-Scanning.gitlab-ci.yml
