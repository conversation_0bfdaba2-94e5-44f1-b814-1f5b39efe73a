{"name": "kk_v1", "version": "0.1.0", "private": true, "scripts": {"dev": "concurrently \"next dev --turbopack\" \"uvicorn backend.main:app --reload\"", "build": "next build --turbopack", "start": "next start", "lint": "next lint"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@hookform/resolvers": "^4.1.3", "@internationalized/date": "^3.8.0", "@mantine/core": "^7.17.1", "@mantine/form": "^7.17.1", "@mantine/hooks": "^7.17.1", "@mui/material": "^6.4.6", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-tooltip": "^1.1.8", "@shadcn/ui": "^0.0.4", "autoprefixer": "^10.4.20", "axios": "^1.8.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "embla-carousel-autoplay": "^8.6.0", "embla-carousel-react": "^8.5.2", "embla-carousel-reactive-utils": "^8.6.0", "framer-motion": "^12.12.1", "geist": "^1.3.1", "lucide-react": "^0.477.0", "motion": "^12.4.10", "next": "^15.3.2", "react": "^18.3.1", "react-aria-components": "^1.8.0", "react-calendar": "^5.1.0", "react-datepicker": "^8.3.0", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.54.2", "react-router-dom": "^6.30.0", "tailwind-merge": "^3.0.2", "tailwind-variants": "^0.3.1", "tailwindcss-animate": "^1.0.7", "turbo": "^2.5.3", "zod": "^3.24.2", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/google.maps": "^3.58.1", "@types/jest": "^29.5.14", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "concurrently": "^9.2.0", "eslint": "^9", "eslint-config-next": "15.1.7", "postcss": "^8.5.3", "postcss-preset-mantine": "^1.17.0", "postcss-simple-vars": "^7.0.1", "tailwindcss": "^3.4.17", "typescript": "^5"}}