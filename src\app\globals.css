@tailwind base;
@tailwind components;
@tailwind utilities;

/* 
  Color palette:
  - Primary: Warm brown (#8B4513)
  - Secondary: Soft cream (#FFF8E7)
  - Accent: Amber/gold (#FFC107)
  - Background: Light beige (#FAF5EB)
  - Text: Dark brown (#3A2618)
  - Muted text: Medium brown (#6B5344)
*/

@layer base {
  :root {
    --background: 36 40% 95%;
    --foreground: 27 50% 16%;

    --card: 36 100% 98%;
    --card-foreground: 27 50% 16%;

    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;

    --primary: 25 75% 31%;
    --primary-foreground: 36 100% 98%;

    --secondary: 36 100% 95%;
    --secondary-foreground: 27 50% 16%;

    --muted: 36 30% 90%;
    --muted-foreground: 27 30% 35%;

    --accent: 35 80% 45%;  /* Changed from 43 100% 52% to a more muted gold */
    --accent-foreground: 27 50% 16%;

    --destructive: 0 85% 60%;
    --destructive-foreground: 36 100% 98%;

    --border: 36 30% 85%;
    --input: 36 30% 85%;
    --ring: 25 75% 31%;

    --radius: 0.75rem;

    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

/* Scrollbar styles */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
  background: #555;
}

@layer base {
  * {
    @apply border-border;
  }

  html {
    scroll-behavior: smooth;
  }

  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-serif;
  }
}

/* Custom utility classes */
.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* Add these new styles for customer management */
.customer-table {
  @apply bg-card border-border rounded-lg;
}

.customer-table th {
  @apply bg-muted text-muted-foreground font-medium px-4 py-3 text-left;
}

.customer-table td {
  @apply border-t border-border px-4 py-3 text-foreground;
}

.customer-table tr:hover {
  @apply bg-muted/50 transition-colors;
}

.customer-form-field {
  @apply bg-background border-input rounded-md focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2;
}

.customer-form-label {
  @apply text-foreground font-medium;
}

.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

/* Parallax container */
.parallax-container {
  overflow: hidden;
  position: relative;
  height: 100%;
  width: 100%;
}

/* Animation utilities */
@layer utilities {
  .animate-fade-in {
    animation: fadeIn 0.8s ease-in-out forwards;
  }

  .animate-slide-up {
    animation: slideUp 0.8s ease-out forwards;
  }

  .animate-slide-in-right {
    animation: slideInRight 0.8s ease-out forwards;
  }

  .animate-slide-in-left {
    animation: slideInLeft 0.8s ease-out forwards;
  }

  .animate-scale {
    animation: scale 0.5s ease-out forwards;
  }
}

/* Animation keyframes */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    transform: translateY(30px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideInRight {
  from {
    transform: translateX(50px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInLeft {
  from {
    transform: translateX(-50px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes scale {
  from {
    transform: scale(0.9);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

/* Hover animations */
.hover-scale {
  transition: transform 0.3s ease;
}

.hover-scale:hover {
  transform: scale(1.05);
}

.hover\:hover-lift:hover {
  transform: translateY(-4px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}
